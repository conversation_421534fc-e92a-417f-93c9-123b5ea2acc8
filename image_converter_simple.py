#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版图片格式转换器 - 稳定版本
支持格式：WebP, PNG, JPEG, BMP, TIFF, GIF
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import threading
from pathlib import Path

class SimpleImageConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("图片格式转换器 - 简化版")
        self.root.geometry("800x600")
        
        # 支持的格式
        self.formats = {
            'WebP': '.webp',
            'PNG': '.png', 
            'JPEG': '.jpg',
            'BMP': '.bmp',
            'TIFF': '.tiff',
            'GIF': '.gif'
        }
        
        self.current_image = None
        self.current_path = None
        self.preview_photo = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 1. 文件选择
        file_frame = ttk.LabelFrame(main_frame, text="选择图片文件", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_var, state='readonly').pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        ttk.Button(file_frame, text="浏览", command=self.browse_file).pack(side=tk.RIGHT)
        
        # 2. 转换设置
        settings_frame = ttk.LabelFrame(main_frame, text="转换设置", padding="10")
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(settings_frame, text="目标格式:").pack(side=tk.LEFT, padx=(0, 10))
        self.format_var = tk.StringVar(value='PNG')
        ttk.Combobox(settings_frame, textvariable=self.format_var, 
                    values=list(self.formats.keys()), state='readonly', width=15).pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(settings_frame, text="JPEG质量:").pack(side=tk.LEFT, padx=(0, 10))
        self.quality_var = tk.IntVar(value=95)
        quality_scale = ttk.Scale(settings_frame, from_=1, to=100, variable=self.quality_var, 
                                 orient=tk.HORIZONTAL, length=100)
        quality_scale.pack(side=tk.LEFT, padx=(0, 10))
        self.quality_label = ttk.Label(settings_frame, text="95%")
        self.quality_label.pack(side=tk.LEFT, padx=(0, 20))
        
        quality_scale.configure(command=self.update_quality)
        
        ttk.Button(settings_frame, text="转换", command=self.convert_image).pack(side=tk.RIGHT)
        ttk.Button(settings_frame, text="批量转换", command=self.batch_convert).pack(side=tk.RIGHT, padx=(0, 10))
        
        # 3. 内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧预览
        preview_frame = ttk.LabelFrame(content_frame, text="图片预览", padding="10")
        preview_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        self.preview_label = ttk.Label(preview_frame, text="请选择图片文件", anchor=tk.CENTER)
        self.preview_label.pack(fill=tk.BOTH, expand=True)
        
        # 右侧信息
        info_frame = ttk.LabelFrame(content_frame, text="图片信息", padding="10")
        info_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        
        self.info_text = tk.Text(info_frame, width=30, height=20, wrap=tk.WORD, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN).pack(fill=tk.X, pady=(10, 0))
        
    def update_quality(self, value):
        """更新质量显示"""
        self.quality_label.config(text=f"{int(float(value))}%")
        
    def browse_file(self):
        """选择文件"""
        filetypes = [
            ("图片文件", "*.webp;*.png;*.jpg;*.jpeg;*.bmp;*.tiff;*.gif"),
            ("所有文件", "*.*")
        ]
        
        filename = filedialog.askopenfilename(title="选择图片文件", filetypes=filetypes)
        if filename:
            self.file_var.set(filename)
            self.current_path = filename
            self.load_image(filename)
            
    def load_image(self, filepath):
        """加载图片"""
        try:
            self.status_var.set("正在加载图片...")
            
            # 加载图片
            self.current_image = Image.open(filepath)
            
            # 显示预览
            self.show_preview()
            
            # 显示信息
            self.show_info()
            
            self.status_var.set("图片加载完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载图片失败: {e}")
            self.status_var.set("加载失败")
            
    def show_preview(self):
        """显示预览"""
        if not self.current_image:
            return
            
        try:
            # 计算预览尺寸
            img_width, img_height = self.current_image.size
            max_size = 400
            
            if img_width > max_size or img_height > max_size:
                ratio = min(max_size/img_width, max_size/img_height)
                new_width = int(img_width * ratio)
                new_height = int(img_height * ratio)
                preview_img = self.current_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            else:
                preview_img = self.current_image
                
            # 转换为PhotoImage
            self.preview_photo = ImageTk.PhotoImage(preview_img)
            self.preview_label.configure(image=self.preview_photo, text="")
            
        except Exception as e:
            self.preview_label.configure(image="", text=f"预览失败: {e}")
            
    def show_info(self):
        """显示图片信息"""
        if not self.current_image or not self.current_path:
            return
            
        try:
            info = []
            info.append("=== 图片信息 ===")
            info.append(f"文件名: {os.path.basename(self.current_path)}")
            info.append(f"格式: {self.current_image.format}")
            info.append(f"尺寸: {self.current_image.size[0]} × {self.current_image.size[1]}")
            info.append(f"模式: {self.current_image.mode}")
            
            # 文件大小
            size = os.path.getsize(self.current_path)
            if size < 1024:
                size_str = f"{size} B"
            elif size < 1024 * 1024:
                size_str = f"{size / 1024:.1f} KB"
            else:
                size_str = f"{size / (1024 * 1024):.1f} MB"
            info.append(f"大小: {size_str}")
            
            # 透明通道
            has_alpha = self.current_image.mode in ('RGBA', 'LA') or 'transparency' in self.current_image.info
            info.append(f"透明通道: {'是' if has_alpha else '否'}")
            
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, '\n'.join(info))
            self.info_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, f"信息获取失败: {e}")
            self.info_text.config(state=tk.DISABLED)
            
    def convert_image(self):
        """转换图片"""
        if not self.current_image or not self.current_path:
            messagebox.showwarning("警告", "请先选择图片文件！")
            return
            
        target_format = self.format_var.get()
        target_ext = self.formats[target_format]
        
        # 选择保存位置
        save_path = filedialog.asksaveasfilename(
            title="保存转换后的图片",
            defaultextension=target_ext,
            filetypes=[(f"{target_format}图片", f"*{target_ext}"), ("所有文件", "*.*")],
            initialname=f"{Path(self.current_path).stem}_converted{target_ext}"
        )
        
        if save_path:
            threading.Thread(target=self._do_convert, args=(self.current_path, save_path, target_format), daemon=True).start()
            
    def _do_convert(self, input_path, output_path, target_format):
        """执行转换"""
        try:
            self.status_var.set("正在转换...")
            
            with Image.open(input_path) as img:
                if target_format == 'JPEG':
                    if img.mode in ('RGBA', 'LA'):
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'RGBA':
                            background.paste(img, mask=img.split()[-1])
                        else:
                            background.paste(img)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')
                    img.save(output_path, format='JPEG', quality=self.quality_var.get())
                    
                elif target_format == 'BMP':
                    if img.mode in ('RGBA', 'LA'):
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'RGBA':
                            background.paste(img, mask=img.split()[-1])
                        else:
                            background.paste(img)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')
                    img.save(output_path, format='BMP')
                    
                else:
                    img.save(output_path, format=target_format)
            
            self.status_var.set("转换完成")
            self.root.after(0, lambda: messagebox.showinfo("成功", f"图片已转换为 {target_format} 格式！\n保存位置: {output_path}"))
            
        except Exception as e:
            self.status_var.set("转换失败")
            self.root.after(0, lambda: messagebox.showerror("错误", f"转换失败: {e}"))

    def batch_convert(self):
        """批量转换"""
        input_folder = filedialog.askdirectory(title="选择包含图片的文件夹")
        if not input_folder:
            return

        output_folder = filedialog.askdirectory(title="选择输出文件夹")
        if not output_folder:
            return

        target_format = self.format_var.get()
        threading.Thread(target=self._do_batch_convert, args=(input_folder, output_folder, target_format), daemon=True).start()

    def _do_batch_convert(self, input_folder, output_folder, target_format):
        """执行批量转换"""
        try:
            self.status_var.set("正在扫描文件...")

            # 扫描图片文件
            image_files = []
            supported_exts = ['.webp', '.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif']

            for root, _, files in os.walk(input_folder):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in supported_exts):
                        image_files.append(os.path.join(root, file))

            if not image_files:
                self.status_var.set("未找到图片文件")
                self.root.after(0, lambda: messagebox.showinfo("提示", "在指定文件夹中未找到支持的图片文件"))
                return

            target_ext = self.formats[target_format]
            success_count = 0

            for i, input_path in enumerate(image_files):
                try:
                    self.status_var.set(f"正在转换 {i+1}/{len(image_files)}")

                    output_path = os.path.join(output_folder, f"{Path(input_path).stem}_converted{target_ext}")

                    with Image.open(input_path) as img:
                        if target_format == 'JPEG':
                            if img.mode in ('RGBA', 'LA'):
                                background = Image.new('RGB', img.size, (255, 255, 255))
                                if img.mode == 'RGBA':
                                    background.paste(img, mask=img.split()[-1])
                                else:
                                    background.paste(img)
                                img = background
                            elif img.mode != 'RGB':
                                img = img.convert('RGB')
                            img.save(output_path, format='JPEG', quality=self.quality_var.get())

                        elif target_format == 'BMP':
                            if img.mode in ('RGBA', 'LA'):
                                background = Image.new('RGB', img.size, (255, 255, 255))
                                if img.mode == 'RGBA':
                                    background.paste(img, mask=img.split()[-1])
                                else:
                                    background.paste(img)
                                img = background
                            elif img.mode != 'RGB':
                                img = img.convert('RGB')
                            img.save(output_path, format='BMP')

                        else:
                            img.save(output_path, format=target_format)

                    success_count += 1

                except Exception as e:
                    print(f"转换失败 {input_path}: {e}")

            self.status_var.set("批量转换完成")
            self.root.after(0, lambda: messagebox.showinfo("完成", f"批量转换完成！\n成功转换 {success_count}/{len(image_files)} 个文件"))

        except Exception as e:
            self.status_var.set("批量转换失败")
            self.root.after(0, lambda: messagebox.showerror("错误", f"批量转换失败: {e}"))


def main():
    """主程序"""
    try:
        root = tk.Tk()
        app = SimpleImageConverter(root)
        root.mainloop()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
