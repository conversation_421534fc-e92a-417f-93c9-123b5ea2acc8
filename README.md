# 图片格式转换器

一个功能强大的图片格式转换GUI工具，支持多种常见图片格式之间的互相转换。

## 🌟 主要功能

- **多格式支持**: WebP, PNG, JPEG, BMP, TIFF, GIF
- **实时预览**: 加载图片后立即显示预览
- **详细信息**: 显示图片的尺寸、格式、大小等详细参数
- **单个转换**: 选择单个文件进行格式转换
- **批量转换**: 批量处理整个文件夹的图片
- **质量控制**: JPEG/WebP格式支持质量调节
- **转换日志**: 实时显示转换进度和结果

## 🚀 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

或者直接安装Pillow：

```bash
pip install Pillow
```

### 运行程序

```bash
python image_converter.py
```

## 📖 使用说明

### 单个文件转换

1. 点击"浏览"按钮选择要转换的图片文件
2. 在右侧查看图片的详细信息
3. 选择目标格式（WebP, PNG, JPEG等）
4. 如果是JPEG格式，可以调节质量滑块
5. 点击"开始转换"按钮
6. 选择保存位置并确认

### 批量转换

1. 点击"批量转换"按钮
2. 选择包含图片的源文件夹
3. 选择输出文件夹
4. 程序会自动扫描并转换所有支持的图片文件

### 支持的格式

| 格式 | 扩展名 | 透明通道 | 质量调节 | 说明 |
|------|--------|----------|----------|------|
| WebP | .webp | ✅ | ✅ | 现代高效格式 |
| PNG | .png | ✅ | ❌ | 无损压缩 |
| JPEG | .jpg/.jpeg | ❌ | ✅ | 有损压缩 |
| BMP | .bmp | ❌ | ❌ | 位图格式 |
| TIFF | .tiff | ✅ | ❌ | 专业格式 |
| GIF | .gif | ✅ | ❌ | 动画支持 |

## 🔧 技术特性

- **智能格式处理**: 自动处理透明通道转换
- **内存优化**: 大图片自动缩放预览
- **多线程**: 转换操作在后台线程执行，不阻塞界面
- **错误处理**: 完善的错误提示和日志记录
- **用户友好**: 直观的GUI界面和操作流程

## 📋 系统要求

- Python 3.7+
- Pillow 10.0.0+
- tkinter (通常随Python安装)

## 🐛 常见问题

### Q: 转换JPEG时透明背景变成黑色？
A: JPEG格式不支持透明通道，程序会自动将透明背景替换为白色。

### Q: 批量转换时部分文件失败？
A: 请检查转换日志，可能是文件损坏或格式不支持。

### Q: 预览图片显示不完整？
A: 大图片会自动缩放以适应预览窗口，不影响转换质量。

## 📝 更新日志

### v1.0 (2024-07-29)
- 初始版本发布
- 支持6种主流图片格式
- 实现单个和批量转换功能
- 添加实时预览和详细信息显示

## 📄 许可证

MIT License - 可自由使用和修改。
