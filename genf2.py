import sys
import os
import pythoncom
from win32com.shell import shell
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QLineEdit, QPushButton, QFileDialog,
    QHBoxLayout, QVBoxLayout, QMessageBox, QGroupBox, QSizePolicy, QTextEdit,
    QCheckBox
)
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtCore import QSize, Qt

def get_default_pythonw():
    possible_paths = [
        os.path.join(sys.exec_prefix, 'pythonw.exe'),
        os.path.join(sys.base_prefix, 'pythonw.exe'),
        r'C:\Python39\pythonw.exe',
        r'C:\Python310\pythonw.exe',
        r'C:\Python311\pythonw.exe',
        r'C:\Python312\pythonw.exe',
        r'C:\Python313\pythonw.exe',
    ]
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return ''

class ShortcutCreator(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Windows 11 Python 快捷方式生成器")
        self.setWindowIcon(QIcon())
        self.setFixedSize(700, 550)
        self.init_ui()
        self.setStyleSheet("""
            QWidget {
                background-color: #f4f6fa;
                font-family: 'Segoe UI', 'Microsoft YaHei', Arial;
                font-size: 14px;
            }
            QGroupBox {
                border: 1px solid #c0c0c0;
                border-radius: 8px;
                margin-top: 12px;
                background-color: #ffffff;
                padding-top: 15px;
            }
            QGroupBox:title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2d5be3;
                font-weight: bold;
                font-size: 15px;
            }
            QPushButton {
                background-color: #2d5be3;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                min-height: 36px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1a3e8a;
            }
            QPushButton:pressed {
                background-color: #0f2d6b;
            }
            QLineEdit {
                background-color: #ffffff;
                border: 2px solid #d1d5db;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                min-height: 32px;
            }
            QLineEdit:focus {
                border-color: #2d5be3;
            }
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #d1d5db;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                font-family: 'Consolas', 'Courier New', monospace;
            }
            QCheckBox {
                font-size: 14px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)

    def init_ui(self):
        # 字体设置
        label_font = QFont("Segoe UI", 13)
        edit_font = QFont("Segoe UI", 14)
        btn_font = QFont("Segoe UI", 14, QFont.Bold)

        # 1. Python脚本选择
        self.py_label = QLabel("选择要创建快捷方式的 Python 脚本：")
        self.py_label.setFont(label_font)
        self.py_edit = QLineEdit()
        self.py_edit.setFont(edit_font)
        self.py_edit.setPlaceholderText("请选择 .py 文件...")
        self.py_btn = QPushButton("浏览文件")
        self.py_btn.setFont(btn_font)
        self.py_btn.setFixedWidth(100)
        self.py_btn.clicked.connect(self.browse_py)

        py_layout = QHBoxLayout()
        py_layout.addWidget(self.py_edit)
        py_layout.addWidget(self.py_btn)
        py_layout.setSpacing(10)

        py_group = QGroupBox("1. 选择 Python 脚本")
        py_group_layout = QVBoxLayout()
        py_group_layout.addWidget(self.py_label)
        py_group_layout.addSpacing(8)
        py_group_layout.addLayout(py_layout)
        py_group_layout.setContentsMargins(15, 20, 15, 15)
        py_group.setLayout(py_group_layout)

        # 2. pythonw.exe选择
        self.pythonw_label = QLabel("pythonw.exe 路径（用于无窗口运行）：")
        self.pythonw_label.setFont(label_font)
        self.pythonw_edit = QLineEdit(get_default_pythonw())
        self.pythonw_edit.setFont(edit_font)
        self.pythonw_edit.setPlaceholderText("pythonw.exe 的完整路径...")
        self.pythonw_btn = QPushButton("浏览文件")
        self.pythonw_btn.setFont(btn_font)
        self.pythonw_btn.setFixedWidth(100)
        self.pythonw_btn.clicked.connect(self.browse_pythonw)

        pythonw_layout = QHBoxLayout()
        pythonw_layout.addWidget(self.pythonw_edit)
        pythonw_layout.addWidget(self.pythonw_btn)
        pythonw_layout.setSpacing(10)

        pythonw_group = QGroupBox("2. 选择 pythonw.exe 路径")
        pythonw_group_layout = QVBoxLayout()
        pythonw_group_layout.addWidget(self.pythonw_label)
        pythonw_group_layout.addSpacing(8)
        pythonw_group_layout.addLayout(pythonw_layout)
        pythonw_group_layout.setContentsMargins(15, 20, 15, 15)
        pythonw_group.setLayout(pythonw_group_layout)

        # 3. ICO图标选择
        self.ico_label = QLabel("自定义图标（可选）：")
        self.ico_label.setFont(label_font)
        self.ico_edit = QLineEdit()
        self.ico_edit.setFont(edit_font)
        self.ico_edit.setPlaceholderText("选择 .ico 图标文件（留空使用默认图标）...")
        self.ico_btn = QPushButton("浏览文件")
        self.ico_btn.setFont(btn_font)
        self.ico_btn.setFixedWidth(100)
        self.ico_btn.clicked.connect(self.browse_ico)

        ico_layout = QHBoxLayout()
        ico_layout.addWidget(self.ico_edit)
        ico_layout.addWidget(self.ico_btn)
        ico_layout.setSpacing(10)

        ico_group = QGroupBox("3. 选择图标文件")
        ico_group_layout = QVBoxLayout()
        ico_group_layout.addWidget(self.ico_label)
        ico_group_layout.addSpacing(8)
        ico_group_layout.addLayout(ico_layout)
        ico_group_layout.setContentsMargins(15, 20, 15, 15)
        ico_group.setLayout(ico_group_layout)

        # 4. 预览区域
        self.preview_label = QLabel("快捷方式目标预览：")
        self.preview_label.setFont(label_font)
        self.preview_text = QLineEdit()
        self.preview_text.setFont(QFont("Consolas", 12))
        self.preview_text.setReadOnly(True)
        self.preview_text.setPlaceholderText("选择文件后将显示快捷方式的目标路径...")

        preview_group = QGroupBox("4. 快捷方式预览")
        preview_group_layout = QVBoxLayout()
        preview_group_layout.addWidget(self.preview_label)
        preview_group_layout.addSpacing(8)
        preview_group_layout.addWidget(self.preview_text)
        preview_group_layout.setContentsMargins(15, 20, 15, 15)
        preview_group.setLayout(preview_group_layout)

        # 5. 生成按钮
        self.create_btn = QPushButton("🚀 生成快捷方式")
        self.create_btn.setFont(QFont("Segoe UI", 16, QFont.Bold))
        self.create_btn.setMinimumHeight(50)
        self.create_btn.clicked.connect(self.create_shortcut)

        # 连接信号以更新预览
        self.py_edit.textChanged.connect(self.update_preview)
        self.pythonw_edit.textChanged.connect(self.update_preview)

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.addWidget(py_group)
        main_layout.addWidget(pythonw_group)
        main_layout.addWidget(ico_group)
        main_layout.addWidget(preview_group)
        main_layout.addSpacing(10)
        main_layout.addWidget(self.create_btn)
        main_layout.addStretch()
        self.setLayout(main_layout)

    def update_preview(self):
        """更新快捷方式目标预览"""
        py_path = self.py_edit.text().strip()
        pythonw_path = self.pythonw_edit.text().strip()

        if py_path and pythonw_path:
            # 根据你的要求格式：pythonw.exe 完整路径 "脚本路径"
            target = f'{pythonw_path} "{py_path}"'
            self.preview_text.setText(target)
        else:
            self.preview_text.clear()

    def browse_py(self):
        path, _ = QFileDialog.getOpenFileName(
            self, "选择 Python 脚本", "", "Python Files (*.py);;All Files (*)"
        )
        if path:
            self.py_edit.setText(path)

    def browse_pythonw(self):
        path, _ = QFileDialog.getOpenFileName(
            self, "选择 pythonw.exe", "", "Executable Files (pythonw.exe);;All Files (*)"
        )
        if path:
            self.pythonw_edit.setText(path)

    def browse_ico(self):
        path, _ = QFileDialog.getOpenFileName(
            self, "选择 ICO 图标", "", "Icon Files (*.ico);;All Files (*)"
        )
        if path:
            self.ico_edit.setText(path)

    def create_shortcut(self):
        py_path = self.py_edit.text().strip()
        pythonw = self.pythonw_edit.text().strip()
        ico = self.ico_edit.text().strip()

        # 验证必需的文件
        if not py_path or not os.path.isfile(py_path):
            QMessageBox.critical(self, "错误", "请选择有效的 Python 脚本文件！")
            return
        if not pythonw or not os.path.isfile(pythonw):
            QMessageBox.critical(self, "错误", "请选择有效的 pythonw.exe 路径！")
            return

        # ICO文件是可选的
        if ico and not os.path.isfile(ico):
            QMessageBox.warning(self, "警告", "指定的 ICO 图标文件不存在，将使用默认图标。")
            ico = ""

        # 确定快捷方式的保存位置和名称
        workdir = os.path.dirname(py_path)
        script_name = os.path.splitext(os.path.basename(py_path))[0]
        shortcut_name = f"{script_name}.lnk"
        shortcut_path = os.path.join(workdir, shortcut_name)

        # 如果快捷方式已存在，询问是否覆盖
        if os.path.exists(shortcut_path):
            reply = QMessageBox.question(
                self, "确认覆盖",
                f"快捷方式 '{shortcut_name}' 已存在。\n是否要覆盖它？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

        try:
            pythoncom.CoInitialize()
            shell_link = pythoncom.CoCreateInstance(
                shell.CLSID_ShellLink, None,
                pythoncom.CLSCTX_INPROC_SERVER, shell.IID_IShellLink
            )

            # 设置快捷方式属性
            shell_link.SetPath(pythonw)
            shell_link.SetArguments(f'"{py_path}"')
            shell_link.SetWorkingDirectory(workdir)
            shell_link.SetDescription(f"Python脚本: {os.path.basename(py_path)}")

            # 设置图标（如果提供了的话）
            if ico:
                shell_link.SetIconLocation(ico, 0)

            # 保存快捷方式
            persist_file = shell_link.QueryInterface(pythoncom.IID_IPersistFile)
            persist_file.Save(shortcut_path, 0)

            # 显示成功消息
            success_msg = f"✅ 快捷方式创建成功！\n\n"
            success_msg += f"📁 保存位置：{shortcut_path}\n"
            success_msg += f"🎯 目标命令：{pythonw} \"{py_path}\"\n"
            success_msg += f"📂 工作目录：{workdir}"
            if ico:
                success_msg += f"\n🎨 自定义图标：{ico}"

            QMessageBox.information(self, "创建成功", success_msg)

        except Exception as e:
            error_msg = f"❌ 创建快捷方式失败！\n\n错误详情：\n{str(e)}"
            QMessageBox.critical(self, "创建失败", error_msg)
        finally:
            try:
                pythoncom.CoUninitialize()
            except:
                pass

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ShortcutCreator()
    window.show()
    sys.exit(app.exec_())
