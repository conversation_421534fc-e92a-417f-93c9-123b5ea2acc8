import sys
import os
import pythoncom
from win32com.shell import shell
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QLineEdit, QPushButton, QFileDialog,
    QHBoxLayout, QVBoxLayout, QMessageBox, QGroupBox, QSizePolicy
)
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtCore import QSize

def get_default_pythonw():
    possible_paths = [
        os.path.join(sys.exec_prefix, 'pythonw.exe'),
        os.path.join(sys.base_prefix, 'pythonw.exe'),
        r'C:\Python39\pythonw.exe',
        r'C:\Python310\pythonw.exe',
        r'C:\Python311\pythonw.exe',
        r'C:\Python312\pythonw.exe',
        r'C:\Python313\pythonw.exe',
    ]
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return ''

class ShortcutCreator(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Windows 11 Python 快捷方式生成器")
        self.setWindowIcon(QIcon())
        self.setFixedSize(650, 400)
        self.init_ui()
        self.setStyleSheet("""
            QWidget {
                background-color: #f4f6fa;
                font-family: 'Segoe UI', 'Microsoft YaHei', Arial;
                font-size: 16px;
            }
            QGroupBox {
                border: 1px solid #c0c0c0;
                border-radius: 6px;
                margin-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox:title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px 0 3px;
                color: #2d5be3;
                font-weight: bold;
            }
            QPushButton {
                background-color: #2d5be3;
                color: white;
                border-radius: 6px;
                padding: 8px 20px;
                font-size: 17px;
                min-height: 38px;
            }
            QPushButton:hover {
                background-color: #1a3e8a;
            }
            QLineEdit {
                background-color: #f8fafc;
                border: 1.5px solid #b0b0b0;
                border-radius: 6px;
                padding: 6px 8px;
                font-size: 17px;
                min-height: 36px;
            }
        """)

    def init_ui(self):
        # 字体设置
        label_font = QFont("Segoe UI", 15)
        edit_font = QFont("Segoe UI", 17)
        btn_font = QFont("Segoe UI", 17, QFont.Bold)

        # 1. Python脚本选择
        self.py_label = QLabel("Python 脚本路径：")
        self.py_label.setFont(label_font)
        self.py_edit = QLineEdit()
        self.py_edit.setFont(edit_font)
        self.py_edit.setMinimumHeight(38)
        self.py_btn = QPushButton("浏览")
        self.py_btn.setFont(btn_font)
        self.py_btn.setMinimumHeight(38)
        self.py_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.py_btn.clicked.connect(self.browse_py)

        py_layout = QHBoxLayout()
        py_layout.addWidget(self.py_edit)
        py_layout.addWidget(self.py_btn)

        py_group = QGroupBox("1. 选择 Python 脚本")
        py_group_layout = QVBoxLayout()
        py_group_layout.addWidget(self.py_label)
        py_group_layout.addLayout(py_layout)
        py_group.setLayout(py_group_layout)

        # 2. pythonw.exe选择
        self.pythonw_label = QLabel("pythonw.exe 路径：")
        self.pythonw_label.setFont(label_font)
        self.pythonw_edit = QLineEdit(get_default_pythonw())
        self.pythonw_edit.setFont(edit_font)
        self.pythonw_edit.setMinimumHeight(38)
        self.pythonw_btn = QPushButton("浏览")
        self.pythonw_btn.setFont(btn_font)
        self.pythonw_btn.setMinimumHeight(38)
        self.pythonw_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.pythonw_btn.clicked.connect(self.browse_pythonw)

        pythonw_layout = QHBoxLayout()
        pythonw_layout.addWidget(self.pythonw_edit)
        pythonw_layout.addWidget(self.pythonw_btn)

        pythonw_group = QGroupBox("2. 选择 pythonw.exe 路径")
        pythonw_group_layout = QVBoxLayout()
        pythonw_group_layout.addWidget(self.pythonw_label)
        pythonw_group_layout.addLayout(pythonw_layout)
        pythonw_group.setLayout(pythonw_group_layout)

        # 3. ICO图标选择
        self.ico_label = QLabel("ICO 图标路径：")
        self.ico_label.setFont(label_font)
        self.ico_edit = QLineEdit()
        self.ico_edit.setFont(edit_font)
        self.ico_edit.setMinimumHeight(38)
        self.ico_btn = QPushButton("浏览")
        self.ico_btn.setFont(btn_font)
        self.ico_btn.setMinimumHeight(38)
        self.ico_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.ico_btn.clicked.connect(self.browse_ico)

        ico_layout = QHBoxLayout()
        ico_layout.addWidget(self.ico_edit)
        ico_layout.addWidget(self.ico_btn)

        ico_group = QGroupBox("3. 选择 ICO 图标")
        ico_group_layout = QVBoxLayout()
        ico_group_layout.addWidget(self.ico_label)
        ico_group_layout.addLayout(ico_layout)
        ico_group.setLayout(ico_group_layout)

        # 4. 生成按钮
        self.create_btn = QPushButton("生成快捷方式")
        self.create_btn.setFont(QFont("Segoe UI", 18, QFont.Bold))
        self.create_btn.setMinimumHeight(48)
        self.create_btn.clicked.connect(self.create_shortcut)

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(18)
        main_layout.setContentsMargins(24, 18, 24, 18)
        main_layout.addWidget(py_group)
        main_layout.addWidget(pythonw_group)
        main_layout.addWidget(ico_group)
        main_layout.addSpacing(16)
        main_layout.addWidget(self.create_btn)
        main_layout.addStretch()
        self.setLayout(main_layout)

    def browse_py(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择 Python 脚本", "", "Python Files (*.py)")
        if path:
            self.py_edit.setText(path)

    def browse_pythonw(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择 pythonw.exe", "", "Executable (pythonw.exe)")
        if path:
            self.pythonw_edit.setText(path)

    def browse_ico(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择 ICO 图标", "", "ICO Files (*.ico)")
        if path:
            self.ico_edit.setText(path)

    def create_shortcut(self):
        py_path = self.py_edit.text().strip()
        pythonw = self.pythonw_edit.text().strip()
        ico = self.ico_edit.text().strip()

        if not py_path or not os.path.isfile(py_path):
            QMessageBox.critical(self, "错误", "请选择有效的 Python 脚本文件！")
            return
        if not pythonw or not os.path.isfile(pythonw):
            QMessageBox.critical(self, "错误", "请选择有效的 pythonw.exe 路径！")
            return
        if not ico or not os.path.isfile(ico):
            QMessageBox.critical(self, "错误", "请选择有效的 ICO 图标文件！")
            return

        workdir = os.path.dirname(py_path)
        shortcut_name = os.path.splitext(os.path.basename(py_path))[0] + ".lnk"
        shortcut_path = os.path.join(workdir, shortcut_name)

        try:
            pythoncom.CoInitialize()
            shell_link = pythoncom.CoCreateInstance(
                shell.CLSID_ShellLink, None,
                pythoncom.CLSCTX_INPROC_SERVER, shell.IID_IShellLink
            )
            shell_link.SetPath(pythonw)
            shell_link.SetArguments(f'"{py_path}"')
            shell_link.SetWorkingDirectory(workdir)
            shell_link.SetIconLocation(ico, 0)

            persist_file = shell_link.QueryInterface(pythoncom.IID_IPersistFile)
            persist_file.Save(shortcut_path, 0)
            QMessageBox.information(self, "成功", f"快捷方式已生成：\n{shortcut_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成快捷方式失败：\n{e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ShortcutCreator()
    window.show()
    sys.exit(app.exec_())
