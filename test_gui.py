#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的GUI测试版本 - 用于验证布局
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QLineEdit, QPushButton,
    QHBoxLayout, QVBoxLayout, QGroupBox
)
from PyQt5.QtGui import QFont

class TestGUI(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("布局测试")
        self.setFixedSize(750, 600)
        self.init_ui()
        self.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial;
                font-size: 13px;
            }
            QGroupBox {
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                margin-top: 15px;
                background-color: #ffffff;
                font-weight: bold;
                font-size: 14px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 20px;
                top: 5px;
                padding: 5px 10px;
                color: #2563eb;
                background-color: #ffffff;
                border-radius: 5px;
            }
            QLabel {
                color: #374151;
                font-size: 13px;
                margin-bottom: 5px;
            }
            QPushButton {
                background-color: #2563eb;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: bold;
                min-height: 20px;
            }
            QLineEdit {
                background-color: #ffffff;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                padding: 10px 12px;
                font-size: 13px;
                min-height: 20px;
            }
        """)

    def init_ui(self):
        # 组1
        label1 = QLabel("这是第一个标签：")
        edit1 = QLineEdit()
        edit1.setPlaceholderText("输入框1...")
        btn1 = QPushButton("按钮1")
        btn1.setFixedWidth(80)

        layout1 = QHBoxLayout()
        layout1.addWidget(edit1, 1)
        layout1.addWidget(btn1, 0)
        layout1.setSpacing(10)

        group1 = QGroupBox("1. 第一组")
        group1_layout = QVBoxLayout()
        group1_layout.addWidget(label1)
        group1_layout.addSpacing(10)
        group1_layout.addLayout(layout1)
        group1_layout.setContentsMargins(20, 25, 20, 20)
        group1.setLayout(group1_layout)

        # 组2
        label2 = QLabel("这是第二个标签：")
        edit2 = QLineEdit()
        edit2.setPlaceholderText("输入框2...")
        btn2 = QPushButton("按钮2")
        btn2.setFixedWidth(80)

        layout2 = QHBoxLayout()
        layout2.addWidget(edit2, 1)
        layout2.addWidget(btn2, 0)
        layout2.setSpacing(10)

        group2 = QGroupBox("2. 第二组")
        group2_layout = QVBoxLayout()
        group2_layout.addWidget(label2)
        group2_layout.addSpacing(10)
        group2_layout.addLayout(layout2)
        group2_layout.setContentsMargins(20, 25, 20, 20)
        group2.setLayout(group2_layout)

        # 主按钮
        main_btn = QPushButton("主要按钮")
        main_btn.setFont(QFont("Microsoft YaHei UI", 14, QFont.Bold))
        main_btn.setMinimumHeight(45)

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(12)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.addWidget(group1)
        main_layout.addWidget(group2)
        main_layout.addSpacing(15)
        main_layout.addWidget(main_btn)
        main_layout.addStretch(1)
        self.setLayout(main_layout)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TestGUI()
    window.show()
    sys.exit(app.exec_())
