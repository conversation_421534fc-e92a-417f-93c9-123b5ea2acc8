#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版GUI测试 - 用于排查问题
"""

import sys
import tkinter as tk
from tkinter import ttk, messagebox

def test_basic_gui():
    """测试基本GUI功能"""
    try:
        print("正在创建主窗口...")
        root = tk.Tk()
        root.title("GUI测试")
        root.geometry("400x300")
        
        print("正在添加控件...")
        label = ttk.Label(root, text="GUI测试成功！")
        label.pack(pady=20)
        
        button = ttk.Button(root, text="点击测试", 
                           command=lambda: messagebox.showinfo("测试", "按钮工作正常！"))
        button.pack(pady=10)
        
        print("GUI创建成功，启动主循环...")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic_gui()
