#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片格式转换器 - 支持多种格式互相转换
支持格式：WebP, PNG, JPEG, BMP, TIFF, GIF
功能：预览、批量转换、参数显示
"""

import sys
import os
from pathlib import Path
from PIL import Image, ImageTk
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkinter.scrolledtext import ScrolledText
import threading
from datetime import datetime

class ImageConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("图片格式转换器 v1.0")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # 支持的格式
        self.supported_formats = {
            'WebP': '.webp',
            'PNG': '.png', 
            'JPEG': '.jpg',
            'BMP': '.bmp',
            'TIFF': '.tiff',
            'GIF': '.gif'
        }
        
        # 当前图片信息
        self.current_image = None
        self.current_image_path = None
        self.preview_image = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 1. 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="📁 选择图片文件", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="源文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, state='readonly')
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2)
        ttk.Button(file_frame, text="批量转换", command=self.batch_convert).grid(row=0, column=3, padx=(10, 0))
        
        # 2. 转换设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="⚙️ 转换设置", padding="10")
        settings_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 目标格式选择
        ttk.Label(settings_frame, text="目标格式:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.target_format_var = tk.StringVar(value='PNG')
        format_combo = ttk.Combobox(settings_frame, textvariable=self.target_format_var, 
                                   values=list(self.supported_formats.keys()), state='readonly', width=15)
        format_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        # 质量设置（JPEG专用）
        ttk.Label(settings_frame, text="JPEG质量:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.quality_var = tk.IntVar(value=95)
        quality_scale = ttk.Scale(settings_frame, from_=1, to=100, variable=self.quality_var, 
                                 orient=tk.HORIZONTAL, length=150)
        quality_scale.grid(row=0, column=3, sticky=tk.W, padx=(0, 10))
        self.quality_label = ttk.Label(settings_frame, text="95%")
        self.quality_label.grid(row=0, column=4, sticky=tk.W)
        
        # 绑定质量滑块事件
        quality_scale.configure(command=self.update_quality_label)
        
        # 转换按钮
        ttk.Button(settings_frame, text="🔄 开始转换", command=self.convert_image, 
                  style='Accent.TButton').grid(row=0, column=5, padx=(20, 0))
        
        # 3. 主内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # 左侧：图片预览
        preview_frame = ttk.LabelFrame(content_frame, text="🖼️ 图片预览", padding="10")
        preview_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        
        # 创建Label用于显示图片（更稳定的方式）
        self.preview_label = ttk.Label(preview_frame, text="请选择图片文件进行预览",
                                      anchor=tk.CENTER, background='white')
        self.preview_label.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 右侧：图片信息和日志
        info_frame = ttk.LabelFrame(content_frame, text="📊 图片信息 & 转换日志", padding="10")
        info_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(1, weight=1)
        
        # 图片信息显示
        self.info_text = ScrolledText(info_frame, height=8, wrap=tk.WORD, state=tk.DISABLED)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 转换日志
        ttk.Label(info_frame, text="转换日志:").grid(row=1, column=0, sticky=(tk.W, tk.N))
        self.log_text = ScrolledText(info_frame, height=15, wrap=tk.WORD, state=tk.DISABLED)
        self.log_text.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def update_quality_label(self, value):
        """更新质量标签"""
        self.quality_label.config(text=f"{int(float(value))}%")
        
    def browse_file(self):
        """浏览选择图片文件"""
        filetypes = [
            ("所有支持的图片", "*.webp;*.png;*.jpg;*.jpeg;*.bmp;*.tiff;*.gif"),
            ("WebP图片", "*.webp"),
            ("PNG图片", "*.png"),
            ("JPEG图片", "*.jpg;*.jpeg"),
            ("BMP图片", "*.bmp"),
            ("TIFF图片", "*.tiff"),
            ("GIF图片", "*.gif"),
            ("所有文件", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=filetypes
        )
        
        if filename:
            self.file_path_var.set(filename)
            self.current_image_path = filename
            self.load_and_preview_image(filename)
            
    def load_and_preview_image(self, filepath):
        """加载并预览图片"""
        try:
            self.status_var.set("正在加载图片...")
            
            # 加载图片
            self.current_image = Image.open(filepath)
            
            # 显示图片信息
            self.display_image_info()
            
            # 预览图片
            self.preview_image_on_label()
            
            self.log_message(f"✅ 成功加载图片: {os.path.basename(filepath)}")
            self.status_var.set("图片加载完成")
            
        except Exception as e:
            error_msg = f"❌ 加载图片失败: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
            self.status_var.set("加载失败")
            
    def display_image_info(self):
        """显示图片信息"""
        if not self.current_image:
            return
            
        info = []
        info.append("=== 图片基本信息 ===")
        info.append(f"文件名: {os.path.basename(self.current_image_path)}")
        info.append(f"格式: {self.current_image.format}")
        info.append(f"尺寸: {self.current_image.size[0]} × {self.current_image.size[1]} 像素")
        info.append(f"颜色模式: {self.current_image.mode}")
        
        # 文件大小
        file_size = os.path.getsize(self.current_image_path)
        if file_size < 1024:
            size_str = f"{file_size} B"
        elif file_size < 1024 * 1024:
            size_str = f"{file_size / 1024:.1f} KB"
        else:
            size_str = f"{file_size / (1024 * 1024):.1f} MB"
        info.append(f"文件大小: {size_str}")
        
        # 颜色深度
        if hasattr(self.current_image, 'bits'):
            info.append(f"颜色深度: {self.current_image.bits} 位")
        
        # 是否有透明通道
        if self.current_image.mode in ('RGBA', 'LA') or 'transparency' in self.current_image.info:
            info.append("透明通道: 是")
        else:
            info.append("透明通道: 否")
            
        # 更新信息显示
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(tk.END, '\n'.join(info))
        self.info_text.config(state=tk.DISABLED)

    def preview_image_on_label(self):
        """在Label上预览图片"""
        if not self.current_image:
            return

        try:
            img_width, img_height = self.current_image.size

            # 计算预览尺寸（最大400x400）
            max_size = 400
            if img_width > max_size or img_height > max_size:
                ratio = min(max_size/img_width, max_size/img_height)
                new_width = int(img_width * ratio)
                new_height = int(img_height * ratio)
            else:
                new_width = img_width
                new_height = img_height

            # 调整图片大小
            if hasattr(Image, 'Resampling'):
                resample = Image.Resampling.LANCZOS
            else:
                resample = Image.LANCZOS

            display_image = self.current_image.resize((new_width, new_height), resample)

            # 转换为Tkinter可用的格式
            self.preview_image = ImageTk.PhotoImage(display_image)

            # 在Label上显示图片
            self.preview_label.configure(image=self.preview_image, text="")

        except Exception as e:
            print(f"预览图片时出错: {e}")
            # 显示错误信息
            self.preview_label.configure(image="", text=f"预览失败: {str(e)}")

    def convert_image(self):
        """转换图片格式"""
        if not self.current_image or not self.current_image_path:
            messagebox.showwarning("警告", "请先选择要转换的图片文件！")
            return

        target_format = self.target_format_var.get()
        target_ext = self.supported_formats[target_format]

        # 选择保存位置
        save_path = filedialog.asksaveasfilename(
            title="保存转换后的图片",
            defaultextension=target_ext,
            filetypes=[(f"{target_format}图片", f"*{target_ext}"), ("所有文件", "*.*")],
            initialname=f"{Path(self.current_image_path).stem}_converted{target_ext}"
        )

        if not save_path:
            return

        # 在新线程中执行转换
        threading.Thread(target=self._convert_single_image,
                        args=(self.current_image_path, save_path, target_format),
                        daemon=True).start()

    def _convert_single_image(self, input_path, output_path, target_format):
        """转换单个图片（在后台线程中执行）"""
        try:
            self.status_var.set("正在转换图片...")

            # 加载图片
            with Image.open(input_path) as img:
                # 处理不同格式的特殊要求
                if target_format == 'JPEG':
                    # JPEG不支持透明通道，需要转换
                    if img.mode in ('RGBA', 'LA'):
                        # 创建白色背景
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'RGBA':
                            background.paste(img, mask=img.split()[-1])  # 使用alpha通道作为mask
                        else:
                            background.paste(img)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')

                    # 保存JPEG时使用质量设置
                    quality = self.quality_var.get()
                    img.save(output_path, format='JPEG', quality=quality, optimize=True)

                elif target_format == 'PNG':
                    # PNG支持透明通道
                    if img.mode not in ('RGBA', 'RGB', 'L', 'LA'):
                        img = img.convert('RGBA')
                    img.save(output_path, format='PNG', optimize=True)

                elif target_format == 'WebP':
                    # WebP支持透明通道和动画
                    quality = self.quality_var.get() if target_format == 'WebP' else 95
                    img.save(output_path, format='WebP', quality=quality, method=6)

                elif target_format == 'BMP':
                    # BMP不支持透明通道
                    if img.mode in ('RGBA', 'LA'):
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'RGBA':
                            background.paste(img, mask=img.split()[-1])
                        else:
                            background.paste(img)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')
                    img.save(output_path, format='BMP')

                else:
                    # 其他格式使用默认设置
                    img.save(output_path, format=target_format)

            # 获取转换后文件信息
            output_size = os.path.getsize(output_path)
            input_size = os.path.getsize(input_path)

            if output_size < 1024:
                size_str = f"{output_size} B"
            elif output_size < 1024 * 1024:
                size_str = f"{output_size / 1024:.1f} KB"
            else:
                size_str = f"{output_size / (1024 * 1024):.1f} MB"

            compression_ratio = (1 - output_size / input_size) * 100

            success_msg = f"✅ 转换成功！\n"
            success_msg += f"   输入: {os.path.basename(input_path)}\n"
            success_msg += f"   输出: {os.path.basename(output_path)}\n"
            success_msg += f"   格式: {target_format}\n"
            success_msg += f"   大小: {size_str}\n"
            if compression_ratio > 0:
                success_msg += f"   压缩率: {compression_ratio:.1f}%"
            else:
                success_msg += f"   大小变化: +{abs(compression_ratio):.1f}%"

            self.log_message(success_msg)
            self.status_var.set("转换完成")

            # 显示成功消息
            self.root.after(0, lambda: messagebox.showinfo("转换成功",
                f"图片已成功转换为 {target_format} 格式！\n保存位置: {output_path}"))

        except Exception as e:
            error_msg = f"❌ 转换失败: {str(e)}"
            self.log_message(error_msg)
            self.status_var.set("转换失败")
            self.root.after(0, lambda: messagebox.showerror("转换失败", error_msg))

    def batch_convert(self):
        """批量转换图片"""
        # 选择输入文件夹
        input_folder = filedialog.askdirectory(title="选择包含图片的文件夹")
        if not input_folder:
            return

        # 选择输出文件夹
        output_folder = filedialog.askdirectory(title="选择输出文件夹")
        if not output_folder:
            return

        target_format = self.target_format_var.get()

        # 在新线程中执行批量转换
        threading.Thread(target=self._batch_convert_images,
                        args=(input_folder, output_folder, target_format),
                        daemon=True).start()

    def _batch_convert_images(self, input_folder, output_folder, target_format):
        """批量转换图片（在后台线程中执行）"""
        try:
            self.status_var.set("正在扫描文件...")

            # 扫描所有支持的图片文件
            image_files = []
            supported_exts = ['.webp', '.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif']

            for root, _, files in os.walk(input_folder):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in supported_exts):
                        image_files.append(os.path.join(root, file))

            if not image_files:
                self.log_message("❌ 在指定文件夹中未找到支持的图片文件")
                self.status_var.set("未找到图片文件")
                return

            self.log_message(f"📁 开始批量转换，共找到 {len(image_files)} 个图片文件")

            target_ext = self.supported_formats[target_format]
            success_count = 0
            error_count = 0

            for i, input_path in enumerate(image_files):
                try:
                    self.status_var.set(f"正在转换 {i+1}/{len(image_files)}: {os.path.basename(input_path)}")

                    # 构建输出路径
                    rel_path = os.path.relpath(input_path, input_folder)
                    output_path = os.path.join(output_folder,
                                             f"{Path(rel_path).stem}_converted{target_ext}")

                    # 确保输出目录存在
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)

                    # 转换图片
                    self._convert_single_image_sync(input_path, output_path, target_format)
                    success_count += 1

                    self.log_message(f"✅ {i+1}/{len(image_files)} - {os.path.basename(input_path)}")

                except Exception as e:
                    error_count += 1
                    self.log_message(f"❌ {i+1}/{len(image_files)} - {os.path.basename(input_path)}: {str(e)}")

            # 完成总结
            summary = f"🎉 批量转换完成！\n"
            summary += f"   成功: {success_count} 个文件\n"
            summary += f"   失败: {error_count} 个文件\n"
            summary += f"   输出文件夹: {output_folder}"

            self.log_message(summary)
            self.status_var.set("批量转换完成")

            self.root.after(0, lambda: messagebox.showinfo("批量转换完成", summary))

        except Exception as e:
            error_msg = f"❌ 批量转换失败: {str(e)}"
            self.log_message(error_msg)
            self.status_var.set("批量转换失败")
            self.root.after(0, lambda: messagebox.showerror("批量转换失败", error_msg))

    def _convert_single_image_sync(self, input_path, output_path, target_format):
        """同步转换单个图片（用于批量转换）"""
        with Image.open(input_path) as img:
            # 处理不同格式的特殊要求
            if target_format == 'JPEG':
                if img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'RGBA':
                        background.paste(img, mask=img.split()[-1])
                    else:
                        background.paste(img)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')

                quality = self.quality_var.get()
                img.save(output_path, format='JPEG', quality=quality, optimize=True)

            elif target_format == 'PNG':
                if img.mode not in ('RGBA', 'RGB', 'L', 'LA'):
                    img = img.convert('RGBA')
                img.save(output_path, format='PNG', optimize=True)

            elif target_format == 'WebP':
                quality = self.quality_var.get()
                img.save(output_path, format='WebP', quality=quality, method=6)

            elif target_format == 'BMP':
                if img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'RGBA':
                        background.paste(img, mask=img.split()[-1])
                    else:
                        background.paste(img)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                img.save(output_path, format='BMP')

            else:
                img.save(output_path, format=target_format)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)  # 滚动到最新消息
        self.log_text.config(state=tk.DISABLED)


def main():
    """主程序入口"""
    try:
        # 检查依赖库
        try:
            from PIL import Image, ImageTk
            print("✅ PIL/Pillow 库已安装")
        except ImportError:
            print("❌ 缺少 PIL/Pillow 库，请运行: pip install Pillow")
            input("按回车键退出...")
            return

        print("正在创建主窗口...")
        # 创建主窗口
        root = tk.Tk()

        # 设置窗口图标（如果有的话）
        try:
            # 可以在这里设置窗口图标
            pass
        except:
            pass

        print("正在初始化应用程序...")
        # 创建应用程序
        app = ImageConverter(root)

        print("正在添加欢迎消息...")
        # 添加欢迎消息
        app.log_message("🎉 图片格式转换器启动成功！")
        app.log_message("📝 支持格式: WebP, PNG, JPEG, BMP, TIFF, GIF")
        app.log_message("🔧 功能: 单个转换、批量转换、实时预览")

        # 绑定窗口关闭事件
        def on_closing():
            try:
                if messagebox.askokcancel("退出", "确定要退出图片转换器吗？"):
                    root.destroy()
            except:
                root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        print("启动主循环...")
        # 启动主循环
        root.mainloop()

    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")


if __name__ == "__main__":
    main()
