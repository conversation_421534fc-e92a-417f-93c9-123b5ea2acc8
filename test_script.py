#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 用于测试快捷方式生成器
"""

import tkinter as tk
from tkinter import messagebox

def main():
    # 创建一个简单的GUI窗口
    root = tk.Tk()
    root.title("测试脚本")
    root.geometry("300x200")
    
    # 添加标签
    label = tk.Label(root, text="这是一个测试脚本\n用于验证快捷方式是否正常工作", 
                     font=("Arial", 12), justify="center")
    label.pack(expand=True)
    
    # 添加按钮
    def show_message():
        messagebox.showinfo("成功", "快捷方式工作正常！")
    
    button = tk.Button(root, text="点击测试", command=show_message, 
                       font=("Arial", 10), bg="#4CAF50", fg="white")
    button.pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    main()
