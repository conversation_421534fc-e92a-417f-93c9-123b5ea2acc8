#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试图片 - 用于测试图片转换器
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_images():
    """创建各种格式的测试图片"""
    
    # 创建测试目录
    test_dir = "test_images"
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建一个彩色测试图片
    width, height = 400, 300
    
    # 创建带透明通道的PNG图片
    img_rgba = Image.new('RGBA', (width, height), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img_rgba)
    
    # 绘制彩色矩形
    colors = [
        (255, 0, 0, 255),    # 红色
        (0, 255, 0, 255),    # 绿色
        (0, 0, 255, 255),    # 蓝色
        (255, 255, 0, 255),  # 黄色
        (255, 0, 255, 255),  # 紫色
        (0, 255, 255, 255),  # 青色
    ]
    
    rect_width = width // 3
    rect_height = height // 2
    
    for i, color in enumerate(colors):
        x = (i % 3) * rect_width
        y = (i // 3) * rect_height
        draw.rectangle([x, y, x + rect_width, y + rect_height], fill=color)
    
    # 添加文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()
    
    draw.text((width//2 - 80, height//2 - 12), "TEST IMAGE", fill=(0, 0, 0, 255), font=font)
    
    # 保存为不同格式
    formats = {
        'PNG': 'PNG',
        'JPEG': 'JPEG', 
        'WebP': 'WebP',
        'BMP': 'BMP'
    }
    
    for name, format_name in formats.items():
        filename = os.path.join(test_dir, f"test_image.{name.lower()}")
        
        if format_name in ['JPEG', 'BMP']:
            # 这些格式不支持透明通道，创建白色背景
            img_rgb = Image.new('RGB', (width, height), (255, 255, 255))
            img_rgb.paste(img_rgba, mask=img_rgba.split()[-1])
            img_rgb.save(filename, format=format_name, quality=95)
        else:
            img_rgba.save(filename, format=format_name)
        
        print(f"✅ 创建测试图片: {filename}")
    
    print(f"\n🎉 测试图片已创建在 {test_dir} 目录中")
    print("现在可以使用图片转换器测试这些文件了！")

if __name__ == "__main__":
    create_test_images()
